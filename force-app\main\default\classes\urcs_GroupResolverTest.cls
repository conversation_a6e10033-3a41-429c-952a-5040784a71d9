/**
 * @File Name         : urcs_GroupResolverTest.cls
 * @Description       : Test class for urcs_GroupResolver
 * <AUTHOR> ACN DEV TEAM
 * @Group             : 
 * @Last Modified On  : 07-24-2025
 * @Last Modified By  : ACN DEV TEAM
**/

@isTest
public class urcs_GroupResolverTest {
    
    @TestSetup
    static void makeData() {
        // Create test user
        Profile standardProfile = [SELECT Id FROM Profile WHERE Name = 'Standard User' LIMIT 1];
        User testUser = new User(
            FirstName = 'Test',
            LastName = 'User',
            Email = '<EMAIL>',
            Username = '<EMAIL>',
            Alias = 'tuser',
            TimeZoneSidKey = 'Europe/Rome',
            LocaleSidKey = 'it_IT',
            EmailEncodingKey = 'UTF-8',
            LanguageLocaleKey = 'it',
            ProfileId = standardProfile.Id
        );
        insert testUser;
        
        // Create test permission sets following the PGMEMB_ pattern
        PermissionSet testPS1 = new PermissionSet(
            Name = 'PGMEMB_urcs_L1_UASSAutoSostPerProgrammato',
            Label = 'urcs_L1_UASSAutoSostPerProgrammato'
        );
        insert testPS1;
        
        PermissionSet testPS2 = new PermissionSet(
            Name = 'PGMEMB_urcs_L2_CPMilano',
            Label = 'urcs_L2_CPMilano'
        );
        insert testPS2;
        
        PermissionSet testPS3 = new PermissionSet(
            Name = 'PGMEMB_urcs_L3_UfficioSinistri',
            Label = 'urcs_L3_UfficioSinistri'
        );
        insert testPS3;
        
        // Create corresponding public groups
        Group testGroup1 = new Group(
            Name = 'urcs_L1_UASSAutoSostPerProgrammato',
            DeveloperName = 'urcs_L1_UASSAutoSostPerProgrammato',
            Type = 'Regular'
        );
        insert testGroup1;
        
        Group testGroup2 = new Group(
            Name = 'urcs_L2_CPMilano',
            DeveloperName = 'urcs_L2_CPMilano',
            Type = 'Regular'
        );
        insert testGroup2;
        
        Group testGroup3 = new Group(
            Name = 'urcs_L3_UfficioSinistri',
            DeveloperName = 'urcs_L3_UfficioSinistri',
            Type = 'Regular'
        );
        insert testGroup3;
        
        // Create some existing group memberships to test deletion
        GroupMember existingMember = new GroupMember(
            UserOrGroupId = testUser.Id,
            GroupId = testGroup1.Id
        );
        insert existingMember;
    }
    
    @isTest
    static void testAssignPublicGroupMembership_Success() {
        // Get test data
        User testUser = [SELECT Id FROM User WHERE Username = '<EMAIL>' LIMIT 1];
        PermissionSet testPS1 = [SELECT Id FROM PermissionSet WHERE Name = 'PGMEMB_urcs_L1_UASSAutoSostPerProgrammato' LIMIT 1];
        PermissionSet testPS2 = [SELECT Id FROM PermissionSet WHERE Name = 'PGMEMB_urcs_L2_CPMilano' LIMIT 1];
        
        // Assign permission sets to user
        List<PermissionSetAssignment> psaList = new List<PermissionSetAssignment>();
        psaList.add(new PermissionSetAssignment(
            AssigneeId = testUser.Id,
            PermissionSetId = testPS1.Id
        ));
        psaList.add(new PermissionSetAssignment(
            AssigneeId = testUser.Id,
            PermissionSetId = testPS2.Id
        ));
        insert psaList;
        
        Test.startTest();
        urcs_GroupResolver.assignPublicGroupMembership(testUser.Id);
        Test.stopTest();
        
        // Verify group memberships
        List<GroupMember> groupMembers = [
            SELECT Id, GroupId, Group.DeveloperName 
            FROM GroupMember 
            WHERE UserOrGroupId = :testUser.Id 
            AND Group.DeveloperName LIKE 'urcs_%'
        ];
        
        System.assertEquals(2, groupMembers.size(), 'Should have 2 group memberships');
        
        Set<String> groupNames = new Set<String>();
        for (GroupMember gm : groupMembers) {
            groupNames.add(gm.Group.DeveloperName);
        }
        
        System.assert(groupNames.contains('urcs_L1_UASSAutoSostPerProgrammato'), 'Should contain urcs_L1_UASSAutoSostPerProgrammato group');
        System.assert(groupNames.contains('urcs_L2_CPMilano'), 'Should contain urcs_L2_CPMilano group');
    }
    
    @isTest
    static void testAssignPublicGroupMembership_NullUserId() {
        Test.startTest();
        urcs_GroupResolver.assignPublicGroupMembership(null);
        Test.stopTest();
        
        // Should not throw exception and complete successfully
        System.assert(true, 'Method should handle null userId gracefully');
    }
    
    @isTest
    static void testAssignPublicGroupMembership_NoPermissionSets() {
        User testUser = [SELECT Id FROM User WHERE Username = '<EMAIL>' LIMIT 1];
        
        Test.startTest();
        urcs_GroupResolver.assignPublicGroupMembership(testUser.Id);
        Test.stopTest();
        
        // Verify existing memberships are deleted but no new ones added
        List<GroupMember> groupMembers = [
            SELECT Id 
            FROM GroupMember 
            WHERE UserOrGroupId = :testUser.Id 
            AND Group.DeveloperName LIKE 'urcs_%'
        ];
        
        System.assertEquals(0, groupMembers.size(), 'Should have no group memberships');
    }
    
    @isTest
    static void testAssignPublicGroupMembership_NoMatchingGroups() {
        User testUser = [SELECT Id FROM User WHERE Username = '<EMAIL>' LIMIT 1];
        
        // Create permission set that doesn't have matching group
        PermissionSet testPS = new PermissionSet(
            Name = 'PGMEMB_urcs_NonExistentGroup',
            Label = 'urcs_NonExistentGroup'
        );
        insert testPS;
        
        PermissionSetAssignment psa = new PermissionSetAssignment(
            AssigneeId = testUser.Id,
            PermissionSetId = testPS.Id
        );
        insert psa;
        
        Test.startTest();
        urcs_GroupResolver.assignPublicGroupMembership(testUser.Id);
        Test.stopTest();
        
        // Verify existing memberships are deleted but no new ones added
        List<GroupMember> groupMembers = [
            SELECT Id 
            FROM GroupMember 
            WHERE UserOrGroupId = :testUser.Id 
            AND Group.DeveloperName LIKE 'urcs_%'
        ];
        
        System.assertEquals(0, groupMembers.size(), 'Should have no group memberships');
    }
    
    @isTest
    static void testRentalGroupResolverException() {
        // Test the custom exception
        try {
            Exception testException = new System.DmlException('Test DML Exception');
            throw new urcs_GroupResolver.RentalGroupResolverException(testException);
        } catch (urcs_GroupResolver.RentalGroupResolverException e) {
            System.assert(e.getMessage().contains('Test DML Exception'), 'Exception message should contain original message');
        }
    }
    
    @isTest
    static void testAssignPublicGroupMembership_DatabaseError() {
        User testUser = [SELECT Id FROM User WHERE Username = '<EMAIL>' LIMIT 1];
        PermissionSet testPS = [SELECT Id FROM PermissionSet WHERE Name = 'PGMEMB_urcs_L1_UASSAutoSostPerProgrammato' LIMIT 1];
        
        PermissionSetAssignment psa = new PermissionSetAssignment(
            AssigneeId = testUser.Id,
            PermissionSetId = testPS.Id
        );
        insert psa;
        
        // Delete the group to cause an error during group member insertion
        Group testGroup = [SELECT Id FROM Group WHERE DeveloperName = 'urcs_L1_UASSAutoSostPerProgrammato' LIMIT 1];
        delete testGroup;
        
        Test.startTest();
        try {
            urcs_GroupResolver.assignPublicGroupMembership(testUser.Id);
            System.assert(false, 'Should have thrown RentalGroupResolverException');
        } catch (urcs_GroupResolver.RentalGroupResolverException e) {
            System.assert(true, 'Should throw RentalGroupResolverException on database error');
        }
        Test.stopTest();
    }
    
    @isTest
    static void testAssignPublicGroupMembership_HardReset() {
        User testUser = [SELECT Id FROM User WHERE Username = '<EMAIL>' LIMIT 1];
        PermissionSet testPS = [SELECT Id FROM PermissionSet WHERE Name = 'PGMEMB_urcs_L3_UfficioSinistri' LIMIT 1];
        
        // Verify user has existing membership (from test setup)
        List<GroupMember> initialMembers = [
            SELECT Id 
            FROM GroupMember 
            WHERE UserOrGroupId = :testUser.Id 
            AND Group.DeveloperName LIKE 'urcs_%'
        ];
        System.assertEquals(1, initialMembers.size(), 'Should have 1 initial membership');
        
        // Assign different permission set
        PermissionSetAssignment psa = new PermissionSetAssignment(
            AssigneeId = testUser.Id,
            PermissionSetId = testPS.Id
        );
        insert psa;
        
        Test.startTest();
        urcs_GroupResolver.assignPublicGroupMembership(testUser.Id);
        Test.stopTest();
        
        // Verify old membership is deleted and new one is added
        List<GroupMember> finalMembers = [
            SELECT Id, Group.DeveloperName 
            FROM GroupMember 
            WHERE UserOrGroupId = :testUser.Id 
            AND Group.DeveloperName LIKE 'urcs_%'
        ];
        
        System.assertEquals(1, finalMembers.size(), 'Should have 1 final membership');
        System.assertEquals('urcs_L3_UfficioSinistri', finalMembers[0].Group.DeveloperName, 'Should have new group membership');
    }
}
