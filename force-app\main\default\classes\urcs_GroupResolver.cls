/**
 * @File Name         : urcs_GroupResolver.cls
 * @Description       : 
 * <AUTHOR> ACN DEV TEAM
 * @Group             : 
 * @Last Modified On  : 07-24-2025
 * @Last Modified By  : ACN DEV TEAM
 * @cicd_tests         urcs_GroupResolverTest
**/

public with sharing class urcs_GroupResolver {    
   
    /******************************************************************************************
    * @description  This method calculate and assign group membership for rental cs user
    * @param        userId
    *******************************************************************************************/
    public static void assignPublicGroupMembership(Id userId) {
        
        Savepoint sp = Database.setSavepoint();
        
        try {
            
            if (userId != null){
                /*===================  PUBLIC GROUP (hard reset)  ====================*/
                // Cancella tutte le membership attuali
                List<GroupMember> gmToDelete = [SELECT Id 
                                                FROM GroupMember 
                                                WHERE UserOrGroupId = :userId 
                                                AND (Group.DeveloperName LIKE 'urcs_%')];
                if (!gmToDelete.isEmpty()) {
                    Database.delete(gmToDelete, false);
                    System.debug('GroupMember eliminati: ' + gmToDelete.size());
                }
                
                /*===================  PUBLIC GROUP (insert)  ====================*/
                // Recupero permission set assignment
                List<PermissionSetAssignment> psaUser = [SELECT Id,PermissionSet.Name, PermissionSet.Label
                                                         FROM PermissionSetAssignment 
                                                         WHERE AssigneeId = :userId and PermissionSet.Name LIKE 'PGMEMB_%'];
                                                         
                if (!psaUser.isEmpty()) {
                    Set<String> groupNameSet = new Set<String>();
                    for (PermissionSetAssignment psa: psaUser) {    
                        String groupName = psa.PermissionSet.Name.removeStart('PGMEMB_');
                        groupNameSet.add(groupName);
                    }
                    
                    List<Group> groupsToRetrieve = [SELECT Id 
                                                    FROM Group
                                                    WHERE Group.DeveloperName IN: groupNameSet];
                                              
                    if (!groupsToRetrieve.isEmpty()) {
                        List<GroupMember> gmToInsert= new List<GroupMember>();
                        for (Group g: groupsToRetrieve) {   
                            GroupMember gm = new GroupMember(UserOrGroupId = userId, GroupId = g.Id);
                            gmToInsert.add(gm);
                        }
                        
                        if (!gmToInsert.isEmpty()) {
                            Database.insert(gmToInsert, false);
                            System.debug('GroupMember aggiunti: ' + gmToInsert.size());
                        }
                    }
                }
            }
        
        } 
        catch (Exception e) {
            Database.rollback(sp);
            System.debug(LoggingLevel.ERROR,'❌ Rollback: ' + e.getMessage());
            throw new RentalGroupResolverException(e) ;
        }
    
    }
    
    public class RentalGroupResolverException extends Exception {}
}