/**
 * @File Name         : urcs_GroupResolverTest.cls
 * @Description       : Test class for urcs_GroupResolver
 * <AUTHOR> ACN DEV TEAM
 * @Group             : 
 * @Last Modified On  : 07-24-2025
 * @Last Modified By  : ACN DEV TEAM
**/

@isTest
public class urcs_GroupResolverTest {
    
    @TestSetup
    static void makeData() {
        // Create test user with unique username
        Profile standardProfile = [SELECT Id FROM Profile WHERE Name = 'Standard User' LIMIT 1];
        String uniqueId = String.valueOf(System.currentTimeMillis());
        User testUser = new User(
            FirstName = 'Test',
            LastName = 'User',
            Email = 'testuser' + uniqueId + '@example.com',
            Username = 'testuser' + uniqueId + '@example.com.test',
            Alias = 'tuser',
            TimeZoneSidKey = 'Europe/Rome',
            LocaleSidKey = 'it_IT',
            EmailEncodingKey = 'UTF-8',
            LanguageLocaleKey = 'it',
            ProfileId = standardProfile.Id
        );
        insert testUser;

        // Create corresponding public groups (we can create these in tests)
        Group testGroup1 = new Group(
            Name = 'urcs_L1_UASSAutoSostPerProgrammato',
            DeveloperName = 'urcs_L1_UASSAutoSostPerProgrammato',
            Type = 'Regular'
        );
        insert testGroup1;

        Group testGroup2 = new Group(
            Name = 'urcs_L2_CPMilano',
            DeveloperName = 'urcs_L2_CPMilano',
            Type = 'Regular'
        );
        insert testGroup2;

        Group testGroup3 = new Group(
            Name = 'urcs_L3_UfficioSinistri',
            DeveloperName = 'urcs_L3_UfficioSinistri',
            Type = 'Regular'
        );
        insert testGroup3;

        // Create some existing group memberships to test deletion
        GroupMember existingMember = new GroupMember(
            UserOrGroupId = testUser.Id,
            GroupId = testGroup1.Id
        );
        insert existingMember;
    }
    
    @isTest
    static void testAssignPublicGroupMembership_Success() {
        // Get test data
        User testUser = [SELECT Id FROM User WHERE Alias = 'tuser' AND FirstName = 'Test' LIMIT 1];

        // Try to get existing permission sets - use the one you specified
        List<PermissionSet> availablePS = [
            SELECT Id, Name FROM PermissionSet
            WHERE Name IN ('PGMEMB_urcs_L1_UASSAutoSostPerProgrammato', 'PGMEMB_urcs_L2_CPMilano')
            LIMIT 2
        ];

        // Skip test if no permission sets are available
        if (availablePS.isEmpty()) {
            System.debug('No PGMEMB_ permission sets available, skipping test');
            return;
        }

        // Assign available permission sets to user
        List<PermissionSetAssignment> psaList = new List<PermissionSetAssignment>();
        for (PermissionSet ps : availablePS) {
            psaList.add(new PermissionSetAssignment(
                AssigneeId = testUser.Id,
                PermissionSetId = ps.Id
            ));
        }
        insert psaList;

        Test.startTest();
        urcs_GroupResolver.assignPublicGroupMembership(testUser.Id);
        Test.stopTest();

        // Verify group memberships
        List<GroupMember> groupMembers = [
            SELECT Id, GroupId, Group.DeveloperName
            FROM GroupMember
            WHERE UserOrGroupId = :testUser.Id
            AND Group.DeveloperName LIKE 'urcs_%'
        ];

        // Should have group memberships equal to available permission sets
        System.assertEquals(availablePS.size(), groupMembers.size(),
            'Should have ' + availablePS.size() + ' group memberships');

        // Verify the groups match the permission sets
        Set<String> expectedGroupNames = new Set<String>();
        for (PermissionSet ps : availablePS) {
            expectedGroupNames.add(ps.Name.removeStart('PGMEMB_'));
        }

        Set<String> actualGroupNames = new Set<String>();
        for (GroupMember gm : groupMembers) {
            actualGroupNames.add(gm.Group.DeveloperName);
        }

        System.assertEquals(expectedGroupNames, actualGroupNames, 'Group names should match permission set names');
    }
    
    @isTest
    static void testAssignPublicGroupMembership_NullUserId() {
        Test.startTest();
        urcs_GroupResolver.assignPublicGroupMembership(null);
        Test.stopTest();

        // Should not throw exception and complete successfully
        System.assert(true, 'Method should handle null userId gracefully');
    }

    @isTest
    static void testAssignPublicGroupMembership_NoPermissionSets() {
        User testUser = [SELECT Id FROM User WHERE Alias = 'tuser' AND FirstName = 'Test' LIMIT 1];
        
        Test.startTest();
        urcs_GroupResolver.assignPublicGroupMembership(testUser.Id);
        Test.stopTest();
        
        // Verify existing memberships are deleted but no new ones added
        List<GroupMember> groupMembers = [
            SELECT Id 
            FROM GroupMember 
            WHERE UserOrGroupId = :testUser.Id 
            AND Group.DeveloperName LIKE 'urcs_%'
        ];
        
        System.assertEquals(0, groupMembers.size(), 'Should have no group memberships');
    }
    
    @isTest
    static void testAssignPublicGroupMembership_NoMatchingGroups() {
        User testUser = [SELECT Id FROM User WHERE Alias = 'tuser' AND FirstName = 'Test' LIMIT 1];

        // Create a group that won't match any permission set pattern
        Group nonMatchingGroup = new Group(
            Name = 'urcs_NonExistentGroup',
            DeveloperName = 'urcs_NonExistentGroup',
            Type = 'Regular'
        );
        insert nonMatchingGroup;

        // Try to find a permission set that doesn't have a matching group
        // We'll simulate this by using a permission set name that doesn't match our created groups
        List<PermissionSet> anyPGMEMBPS = [
            SELECT Id, Name FROM PermissionSet
            WHERE Name LIKE 'PGMEMB_%'
            AND Name NOT IN ('PGMEMB_urcs_L1_UASSAutoSostPerProgrammato', 'PGMEMB_urcs_L2_CPMilano', 'PGMEMB_urcs_L3_UfficioSinistri')
            LIMIT 1
        ];

        if (!anyPGMEMBPS.isEmpty()) {
            PermissionSetAssignment psa = new PermissionSetAssignment(
                AssigneeId = testUser.Id,
                PermissionSetId = anyPGMEMBPS[0].Id
            );
            insert psa;
        }

        Test.startTest();
        urcs_GroupResolver.assignPublicGroupMembership(testUser.Id);
        Test.stopTest();

        // Verify existing memberships are deleted but no new ones added
        List<GroupMember> groupMembers = [
            SELECT Id
            FROM GroupMember
            WHERE UserOrGroupId = :testUser.Id
            AND Group.DeveloperName LIKE 'urcs_%'
        ];

        System.assertEquals(0, groupMembers.size(), 'Should have no group memberships');
    }
    
    @isTest
    static void testRentalGroupResolverException() {
        // Test the custom exception
        try {
            Exception testException = new System.DmlException('Test DML Exception');
            throw new urcs_GroupResolver.RentalGroupResolverException(testException);
        } catch (urcs_GroupResolver.RentalGroupResolverException e) {
            System.assert(e.getMessage().contains('Test DML Exception'), 'Exception message should contain original message');
        }
    }
    
    @isTest
    static void testAssignPublicGroupMembership_DatabaseError() {
        User testUser = [SELECT Id FROM User WHERE Alias = 'tuser' AND FirstName = 'Test' LIMIT 1];

        // Try to get an existing permission set
        List<PermissionSet> availablePS = [
            SELECT Id FROM PermissionSet
            WHERE Name = 'PGMEMB_urcs_L1_UASSAutoSostPerProgrammato'
            LIMIT 1
        ];

        if (availablePS.isEmpty()) {
            System.debug('No PGMEMB_urcs_L1_UASSAutoSostPerProgrammato permission set available, skipping test');
            return;
        }

        PermissionSetAssignment psa = new PermissionSetAssignment(
            AssigneeId = testUser.Id,
            PermissionSetId = availablePS[0].Id
        );
        insert psa;

        // Delete the group to cause an error during group member insertion
        Group testGroup = [SELECT Id FROM Group WHERE DeveloperName = 'urcs_L1_UASSAutoSostPerProgrammato' LIMIT 1];
        delete testGroup;

        Test.startTest();
        try {
            urcs_GroupResolver.assignPublicGroupMembership(testUser.Id);
            System.assert(false, 'Should have thrown RentalGroupResolverException');
        } catch (urcs_GroupResolver.RentalGroupResolverException e) {
            System.assert(true, 'Should throw RentalGroupResolverException on database error');
        }
        Test.stopTest();
    }
    
    @isTest
    static void testAssignPublicGroupMembership_HardReset() {
        User testUser = [SELECT Id FROM User WHERE Alias = 'tuser' AND FirstName = 'Test' LIMIT 1];

        // Try to get an existing permission set different from the initial one
        List<PermissionSet> availablePS = [
            SELECT Id FROM PermissionSet
            WHERE Name IN ('PGMEMB_urcs_L3_UfficioSinistri', 'PGMEMB_urcs_L2_CPMilano')
            LIMIT 1
        ];

        if (availablePS.isEmpty()) {
            System.debug('No alternative PGMEMB_ permission sets available, skipping test');
            return;
        }

        // Verify user has existing membership (from test setup)
        List<GroupMember> initialMembers = [
            SELECT Id
            FROM GroupMember
            WHERE UserOrGroupId = :testUser.Id
            AND Group.DeveloperName LIKE 'urcs_%'
        ];
        System.assertEquals(1, initialMembers.size(), 'Should have 1 initial membership');

        // Assign different permission set
        PermissionSetAssignment psa = new PermissionSetAssignment(
            AssigneeId = testUser.Id,
            PermissionSetId = availablePS[0].Id
        );
        insert psa;

        Test.startTest();
        urcs_GroupResolver.assignPublicGroupMembership(testUser.Id);
        Test.stopTest();

        // Verify old membership is deleted and new one is added
        List<GroupMember> finalMembers = [
            SELECT Id, Group.DeveloperName
            FROM GroupMember
            WHERE UserOrGroupId = :testUser.Id
            AND Group.DeveloperName LIKE 'urcs_%'
        ];

        System.assertEquals(1, finalMembers.size(), 'Should have 1 final membership');

        // Verify the new group matches the permission set
        String expectedGroupName = availablePS[0].Name.removeStart('PGMEMB_');
        System.assertEquals(expectedGroupName, finalMembers[0].Group.DeveloperName, 'Should have new group membership');
    }

    @isTest
    static void testAssignPublicGroupMembership_WithSpecificPermissionSet() {
        // Test specifically with the permission set you provided
        User testUser = [SELECT Id FROM User WHERE Alias = 'tuser' AND FirstName = 'Test' LIMIT 1];

        // Try to get the specific permission set you mentioned
        List<PermissionSet> specificPS = [
            SELECT Id, Name FROM PermissionSet
            WHERE Name = 'PGMEMB_urcs_L1_UASSAutoSostPerProgrammato'
            LIMIT 1
        ];

        if (specificPS.isEmpty()) {
            System.debug('PGMEMB_urcs_L1_UASSAutoSostPerProgrammato permission set not found, skipping test');
            return;
        }

        // Assign the specific permission set to user
        PermissionSetAssignment psa = new PermissionSetAssignment(
            AssigneeId = testUser.Id,
            PermissionSetId = specificPS[0].Id
        );
        insert psa;

        Test.startTest();
        urcs_GroupResolver.assignPublicGroupMembership(testUser.Id);
        Test.stopTest();

        // Verify group membership was created correctly
        List<GroupMember> groupMembers = [
            SELECT Id, Group.DeveloperName, Group.Name
            FROM GroupMember
            WHERE UserOrGroupId = :testUser.Id
            AND Group.DeveloperName = 'urcs_L1_UASSAutoSostPerProgrammato'
        ];

        System.assertEquals(1, groupMembers.size(), 'Should have 1 group membership for the specific permission set');
        System.assertEquals('urcs_L1_UASSAutoSostPerProgrammato', groupMembers[0].Group.DeveloperName,
            'Should be assigned to the correct group');
    }
}
